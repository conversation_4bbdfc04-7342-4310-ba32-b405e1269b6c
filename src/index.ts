import { Telegra<PERSON>, <PERSON><PERSON> } from "telegraf"; //telegraf
import { message } from "telegraf/filters"; //telegraf
import { config } from "dotenv";
import path from "path";
import { HttpsProxyAgent } from "https-proxy-agent";

// Load environment-specific .env files
const nodeEnv = process.env.NODE_ENV || 'development';
config({ path: path.resolve(process.cwd(), '.env') });
if (nodeEnv === 'development') {
  config({ path: path.resolve(process.cwd(), '.env.development') });
} else if (nodeEnv === 'production') {
  config({ path: path.resolve(process.cwd(), '.env.production') });
}

import { appImg, tgLink, welcomeMessage } from "./medialink";

// ------------ Dynamic Proxy Configuration ------------
let bot: Telegraf;

if ( process.env.PROXY_URL) {
  // Use proxy when enabled
  const agent = new HttpsProxyAgent(process.env.PROXY_URL);
  bot = new Telegraf(process.env.NODE_BOT_TOKEN!, {
    telegram: {
      agent: agent,
    },
  });
  console.log(`Bot initialized with proxy: ${process.env.PROXY_URL}`);
} else {
  // Direct connection without proxy
  bot = new Telegraf(process.env.NODE_BOT_TOKEN!);
  console.log('Bot initialized without proxy');
}
// ------------ End Proxy Configuration ------------

// bot.start(handler) — /start 命令触发
// bot.help(handler) — /help 命令
// bot.command(member, handler) — 任意 slash 命令，如 bot.command('invite', ...)
// bot.hears(pattern, handler) — 匹配文本或正则表达式，类似监听关键词
// bot.on(updateType, handler) — 监听所有更新，例如 message, callback_query, inline_query 等
// bot.action(callbackData, handler) — 监听内联按钮回调（callback_query）中特定数据
// bot.reaction() & bot.on('message_reaction') — 监听消息表情反应（需开启支持）

// Markup.button.webApp-点击后会在 Telegram 内部打开指定的 Web 应用程序
// Markup.button.callback--回调按钮-bot收到的指令
// Markup.button.url-- URL 按钮，用户点击后会跳转到指定的链接
// Markup.button.pay--支付按钮。点击该按钮后，用户会被引导到 Telegram 内部支付界面
// Markup.button.switchToChat-按钮，点击后会打开一个与指定用户的聊天窗口
// Markup.button.loginUrl-登录按钮,点击后会打开一个 URL，用于登录功能，支持 OAuth 认证等

// keyboard--对话框下按钮--相当于外链式按钮--botton中多个功能不能使用--不会自带initData
// inlineKeyboard--对话栏内容按钮--相当于内链式按钮--会自带initData

// start触发效果
const startCtx = async (ctx: any) => {
  await ctx.replyWithAnimation(
    {
      url: appImg.gameImgGif,
    },
    {
      caption: welcomeMessage.start,
      ...Markup.inlineKeyboard([
        [Markup.button.webApp("🎮 Play Game", tgLink.gameHtlmLink)],
        [Markup.button.url("🛠 Support", tgLink.helpLink)],
        [Markup.button.url("📖 FAQ", tgLink.faqLink)],
      ]),
    }
  );
};

// help触发效果
const helpCtx = async (ctx: any) => {
  await ctx.reply(
    welcomeMessage.help,
    Markup.inlineKeyboard([
      [Markup.button.webApp("🎮 Play Game", tgLink.gameHtlmLink)],
    ])
  );
};

// join触发效果
const joinCtx = async (ctx: any) => {
  await ctx.reply(
    welcomeMessage.join,
    Markup.inlineKeyboard([
      [Markup.button.url("Join For Fun", tgLink.joinLink)],
    ])
  );
};

// invite触发效果
const inviteCtx = async (ctx: any) => {
  const { username } = await ctx.telegram.getMe();
  // 组成邀请链接
  const inviteUrl = `https://t.me/${username}?start=referral_${ctx.from.id}`;
  // 邀请链接-html
  const htmlCaption =
    welcomeMessage.invite +
    `
  <b>Invite link</b>
  <code>${inviteUrl}</code>
  `.trim();
  await ctx.replyWithAnimation(
    {
      url: appImg.gameImgGif, // 图片地址
    },
    {
      caption: htmlCaption,
      parse_mode: "HTML",
      ...Markup.inlineKeyboard([
        [
          Markup.button.switchToChat(
            "Invite a friend",
            inviteUrl + welcomeMessage.inviteLink
          ),
        ],
      ]),
    }
  );
};

// /start 指令处理逻辑
bot.start(async (ctx) => {
  // 第一步：欢迎消息触发下方按钮
  await ctx.reply(
    "👏Welcome to the A3A Game!", // 欢迎语言
    Markup.keyboard([
      [
        Markup.button.callback("👉Join Community", "/join"),
        Markup.button.callback("❓ Help", "/help"),
      ],
    ])
      .resize() //button自适应
      // .oneTime() // 可选：点击后隐藏键盘
      .persistent() // 保持键盘显示
  );
  // 第二步：展示消息内容触发对话框内按钮
  await startCtx(ctx);
});

// help 指令处理逻辑
bot.help(async (ctx) => {
  await helpCtx(ctx);
});
// 监听-'/help'--命令处理
bot.hears("❓ Help", async (ctx) => {
  await helpCtx(ctx);
});
bot.action("/help", async (ctx) => {
  await ctx.answerCbQuery(); // ✅ 必须调用，结束加载图标
  await helpCtx(ctx);
});

// Join
bot.hears("👉Join Community", async (ctx) => {
  await joinCtx(ctx);
});

//invite--hears--文字内容处理
bot.hears("/invite", async (ctx) => {
  await inviteCtx(ctx);
});

// 监听文本消息（可选）
bot.on(message("text"), async (ctx) => {
  const text = ctx.message?.text;
  if (text) {
    // await ctx.reply(text);
    await startCtx(ctx);
  }
});

// 启动 bot
bot
  .launch()
  .then(() => {
    console.log("Bot success");
  })
  .catch((err) => {
    console.error("Bot error:", err);
  });
