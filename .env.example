# A3A Telegram Bot Environment Configuration

# Environment (development, production)
NODE_ENV=development

# Telegram Bot Token (get from @BotFather)
NODE_BOT_TOKEN=your_bot_token_here

# Optional: Proxy URL for regions where Telegram is blocked
# PROXY_URL=http://proxy-server:port

# Optional: Custom port for health checks (if needed)
# PORT=3000

# Optional: Log level (error, warn, info, debug)
# LOG_LEVEL=info
