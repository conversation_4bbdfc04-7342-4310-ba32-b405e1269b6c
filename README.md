# A3A Telegram Bot

一个现代化的 Telegram 机器人，使用 TypeScript 和 Telegraf 构建，具有简洁高效的自动化部署方案。

## ✨ 特性

- **TypeScript** - 类型安全的开发体验
- **Telegraf** - 现代化的 Telegram Bot API 框架
- **代理支持** - 可选的代理配置
- **环境配置** - 开发/生产环境分离
- **自动部署** - 简洁的 GitHub Actions 部署方案
- **PM2 管理** - 生产环境进程管理

## 🚀 部署方案特点

- ✅ **安全** - 服务器无需 Git 权限
- ✅ **简洁** - 一键部署，无需复杂配置
- ✅ **高效** - 编译打包传输，速度快
- ✅ **可靠** - PM2 进程管理，自动重启

## 📋 前置要求

- Node.js 22.x (推荐最新 LTS)
- npm (最新版本)
- Telegram Bot Token (从 [@BotFather](https://t.me/botfather) 获取)

## 🛠️ 本地开发

### 1. 克隆和安装

```bash
git clone https://github.com/your-username/a3a-telegram-bot.git
cd a3a-telegram-bot
npm install
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

在 `.env` 文件中配置：
```env
NODE_ENV=development
NODE_BOT_TOKEN=你的机器人token
# PROXY_URL=http://proxy:port (如果需要代理)
```

### 3. 运行开发服务器

```bash
npm run dev
```

## 🚀 生产部署

### 快速部署

1. **服务器准备** - 按照 `DEPLOYMENT-SETUP.md` 准备服务器
2. **配置 Secrets** - 在 GitHub 仓库中配置 SSH 相关的 Secrets
3. **提交 .env 文件** - 确保 .env 文件已提交到仓库
4. **推送部署** - 推送代码到 main 分支即可自动部署

```bash
git push origin main
```

详细部署指南请查看：[DEPLOYMENT-SETUP.md](./DEPLOYMENT-SETUP.md)

## 📁 项目结构

```
a3a-telegram-bot/
├── .github/workflows/     # GitHub Actions 工作流
│   └── deploy.yml        # 自动部署配置
├── src/                  # 源代码
│   ├── index.ts          # 主程序入口
│   └── medialink.ts      # 媒体和链接配置
├── ecosystem.config.js   # PM2 配置文件
├── package.json          # 项目依赖
├── tsconfig.json         # TypeScript 配置
└── .env.example          # 环境变量模板
```

## 🔧 可用脚本

```bash
npm run dev          # 启动开发服务器
npm run dev:prod     # 以生产模式启动开发服务器
npm run build        # 构建 TypeScript 到 JavaScript
npm run start        # 启动生产服务器
npm run start:prod   # 以生产环境启动服务器
```

## 📊 监控和管理

### PM2 命令

```bash
pm2 status           # 查看进程状态
pm2 logs a3a-bot     # 查看日志
pm2 restart a3a-bot  # 重启机器人
pm2 stop a3a-bot     # 停止机器人
pm2 monit            # 监控面板
```

### 健康检查

机器人包含基本的健康监控功能：
- 自动重启机制
- 内存使用监控
- 错误日志记录

## 🔍 故障排除

### 常见问题

1. **机器人无响应**
   ```bash
   # 检查进程状态
   pm2 status

   # 查看日志
   pm2 logs a3a-bot

   # 验证 Token
   curl -s "https://api.telegram.org/bot你的token/getMe"
   ```

2. **部署失败**
   - 检查 GitHub Actions 日志
   - 验证 SSH 连接和权限
   - 确认服务器环境配置

3. **编译错误**
   ```bash
   # 检查 TypeScript 配置
   npx tsc --noEmit

   # 重新构建
   npm run build
   ```

## 🔐 安全最佳实践

- 使用环境变量存储敏感信息
- 定期更新依赖包
- 使用 SSH 密钥而非密码
- 限制服务器访问权限
- 定期检查安全漏洞

## 📄 许可证

本项目使用 ISC 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果遇到问题：
1. 查看 [DEPLOYMENT-SETUP.md](./DEPLOYMENT-SETUP.md) 部署指南
2. 检查 GitHub Actions 日志
3. 查看服务器日志和状态
4. 提交 Issue 寻求帮助

---

**一个简洁、安全、高效的 Telegram Bot 部署方案！** 🎉
