# A3A Telegram Bot

A modern Telegram bot built with TypeScript and Telegraf, featuring automated CI/CD deployment with GitHub Actions.

## ✨ 2025 年最新更新

- **Node.js 22 LTS** - 升级到最新长期支持版本
- **增强安全性** - OIDC、权限最小化、安全扫描
- **改进的 CI/CD** - 最新 GitHub Actions 版本
- **Docker 优化** - 多阶段构建、安全增强
- **性能提升** - 更好的缓存策略和构建优化

## 🚀 Features

- **TypeScript** - Type-safe development
- **Telegraf** - Modern Telegram Bot API framework
- **Proxy Support** - Optional proxy configuration
- **Environment-based Configuration** - Separate configs for dev/prod
- **Automated CI/CD** - GitHub Actions workflows with 2025 best practices
- **Docker Support** - Containerized deployment with security enhancements
- **PM2 Integration** - Process management for production
- **Security First** - OIDC, minimal permissions, vulnerability scanning

## 📋 Prerequisites

- Node.js 20.x or 22.x (22.x LTS recommended)
- npm (latest version)
- Telegram <PERSON><PERSON> (from [@BotFather](https://t.me/botfather))

## 🛠️ Local Development

### 1. Clone and Install

```bash
git clone https://github.com/your-username/a3a-telegram-bot.git
cd a3a-telegram-bot
npm install
```

### 2. Environment Setup

Create environment files:

```bash
# Development
cp .env.example .env.development
# Production
cp .env.example .env.production
```

Edit the files with your bot token:

```env
NODE_ENV=development
NODE_BOT_TOKEN=your_bot_token_here
# PROXY_URL=http://proxy:port (optional)
```

### 3. Run Development Server

```bash
npm run dev
```

## 🚀 Production Deployment

### Option 1: Automated GitHub Actions Deployment

1. **Server Setup**
   ```bash
   # Run on your server
   chmod +x scripts/setup-server.sh
   ./scripts/setup-server.sh
   ```

2. **Configure GitHub Secrets**

   Go to your repository → Settings → Secrets and variables → Actions

   Add these secrets:
   - `HOST` - Your server IP address
   - `USERNAME` - SSH username (e.g., ubuntu)
   - `PRIVATE_KEY` - SSH private key
   - `NODE_BOT_TOKEN` - Your Telegram bot token
   - `PROJECT_PATH` - Project directory (optional, defaults to ~/a3a-telegram-bot)

3. **Deploy**

   Push to main branch or manually trigger deployment:
   ```bash
   git push origin main
   ```

### Option 2: Manual Deployment

```bash
# On your server
git clone https://github.com/your-username/a3a-telegram-bot.git
cd a3a-telegram-bot
npm ci --only=production
npm run build
pm2 start ecosystem.config.js --env production
```

### Option 3: Docker Deployment

```bash
# Build and run
docker build -t a3a-bot .
docker run -d \
  --name a3a-bot \
  --restart unless-stopped \
  -e NODE_BOT_TOKEN="your_token" \
  a3a-bot
```

## 📁 Project Structure

```
a3a-telegram-bot/
├── .github/workflows/     # GitHub Actions workflows
│   ├── ci.yml            # Continuous Integration
│   ├── deploy.yml        # SSH Deployment
│   └── docker-deploy.yml # Docker Deployment
├── scripts/              # Deployment scripts
│   ├── setup-server.sh   # Server setup script
│   └── deploy.sh         # Quick deployment script
├── src/                  # Source code
│   ├── index.ts          # Main bot file
│   └── medialink.ts      # Media and links
├── Dockerfile            # Docker configuration
├── ecosystem.config.js   # PM2 configuration
├── tsconfig.json         # TypeScript configuration
└── package.json          # Dependencies and scripts
```

## 🔧 Available Scripts

```bash
npm run dev          # Start development server
npm run dev:prod     # Start development server in production mode
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server
npm run type-check   # Check TypeScript types
npm run clean        # Clean build directory
```

## 🐳 Docker Commands

```bash
# Build image
docker build -t a3a-bot .

# Run container
docker run -d --name a3a-bot -e NODE_BOT_TOKEN="your_token" a3a-bot

# View logs
docker logs a3a-bot

# Stop container
docker stop a3a-bot
```

## 📊 Monitoring

### PM2 Commands

```bash
pm2 status           # Show all processes
pm2 logs a3a-bot     # Show logs
pm2 restart a3a-bot  # Restart bot
pm2 stop a3a-bot     # Stop bot
pm2 delete a3a-bot   # Delete process
```

### Health Checks

The bot includes basic health monitoring. Check logs for any issues:

```bash
# PM2 logs
pm2 logs a3a-bot

# Docker logs
docker logs a3a-bot
```

## 🔒 Security

- Environment variables for sensitive data
- Non-root Docker user
- SSH key-based authentication
- GitHub environment protection rules
- Regular dependency updates

## 🚨 Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check bot token validity
   - Verify network connectivity
   - Check PM2/Docker process status

2. **Deployment fails**
   - Verify GitHub Secrets
   - Check SSH key permissions
   - Ensure server has Node.js and PM2

3. **TypeScript errors**
   - Run `npm run type-check`
   - Check tsconfig.json configuration

### Getting Help

1. Check the logs: `pm2 logs a3a-bot`
2. Verify environment variables
3. Test bot token with Telegram API
4. Check GitHub Actions logs for deployment issues

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review GitHub Actions logs
- Verify server logs and status
