#!/bin/bash

# A3A Telegram Bot - Server Setup Script
# This script sets up the production environment for the bot

set -e  # Exit on any error

echo "🚀 Setting up A3A Telegram Bot production environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Update system packages
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Node.js 20.x
print_status "Installing Node.js 20.x..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
else
    print_warning "Node.js is already installed"
fi

# Verify Node.js installation
NODE_VERSION=$(node --version)
print_status "Node.js version: $NODE_VERSION"

# Install PM2 globally
print_status "Installing PM2..."
if ! command -v pm2 &> /dev/null; then
    sudo npm install -g pm2
    pm2 startup
    print_warning "Please run the command shown above to enable PM2 startup"
else
    print_warning "PM2 is already installed"
fi

# Install Git if not present
if ! command -v git &> /dev/null; then
    print_status "Installing Git..."
    sudo apt install -y git
fi

# Create logs directory
print_status "Creating logs directory..."
mkdir -p logs

# Set up environment file template
print_status "Creating environment file template..."
if [ ! -f .env.production ]; then
    cat > .env.production << EOF
NODE_ENV=production
NODE_BOT_TOKEN=your_bot_token_here
# PROXY_URL=http://proxy:port
EOF
    print_warning "Please edit .env.production with your actual bot token"
else
    print_warning ".env.production already exists"
fi

# Install project dependencies
if [ -f package.json ]; then
    print_status "Installing project dependencies..."
    npm ci --only=production
    
    # Build TypeScript
    print_status "Building TypeScript..."
    npm run build
else
    print_warning "package.json not found. Make sure you're in the project directory."
fi

# Set up firewall (optional)
read -p "Do you want to configure UFW firewall? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Configuring UFW firewall..."
    sudo ufw --force enable
    sudo ufw allow ssh
    sudo ufw allow 80
    sudo ufw allow 443
    print_status "Firewall configured"
fi

# Generate SSH key for GitHub Actions (optional)
read -p "Do you want to generate SSH key for GitHub Actions? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Generating SSH key for GitHub Actions..."
    ssh-keygen -t rsa -b 4096 -C "github-actions-a3a-bot" -f ~/.ssh/id_rsa_github_actions -N ""
    
    print_status "Adding public key to authorized_keys..."
    cat ~/.ssh/id_rsa_github_actions.pub >> ~/.ssh/authorized_keys
    chmod 600 ~/.ssh/authorized_keys
    
    print_warning "Copy this private key to GitHub Secrets as PRIVATE_KEY:"
    echo "----------------------------------------"
    cat ~/.ssh/id_rsa_github_actions
    echo "----------------------------------------"
fi

print_status "✅ Server setup completed!"
print_status "Next steps:"
echo "1. Edit .env.production with your bot token"
echo "2. Add GitHub Secrets (HOST, USERNAME, PRIVATE_KEY, NODE_BOT_TOKEN)"
echo "3. Push your code to trigger deployment"
echo "4. Monitor with: pm2 status"
