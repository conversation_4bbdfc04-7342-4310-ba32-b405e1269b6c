#!/bin/bash

# A3A Telegram Bot - Quick Deploy Script
# This script performs a quick deployment update

set -e

echo "🚀 Deploying A3A Telegram Bot..."

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Pull latest changes
print_status "Pulling latest changes from Git..."
git pull origin main

# Install dependencies
print_status "Installing dependencies..."
npm ci --only=production

# Build TypeScript
print_status "Building TypeScript..."
npm run build

# Restart PM2 process
print_status "Restarting PM2 process..."
if pm2 list | grep -q "a3a-bot"; then
    pm2 restart a3a-bot
else
    print_warning "PM2 process 'a3a-bot' not found. Starting new process..."
    pm2 start ecosystem.config.js --env production
fi

# Show status
print_status "Deployment completed! Current status:"
pm2 status a3a-bot

print_status "✅ Deployment successful!"
print_status "Monitor logs with: pm2 logs a3a-bot"
