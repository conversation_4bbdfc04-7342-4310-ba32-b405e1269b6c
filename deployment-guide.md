# A3A Telegram Bot - Deployment Guide

## 🚀 GitHub Actions CI/CD Setup

This project includes modern GitHub Actions workflows for automated testing and deployment.

### 📁 Workflow Files

1. **`.github/workflows/ci.yml`** - Continuous Integration
2. **`.github/workflows/deploy.yml`** - Traditional SSH Deployment
3. **`.github/workflows/docker-deploy.yml`** - Docker-based Deployment

## 🔧 Setup Instructions

### 1. Server Preparation

#### Option A: Traditional Node.js Deployment

```bash
# Install Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Clone your repository
git clone https://github.com/your-username/a3a-telegram-bot.git
cd a3a-telegram-bot

# Install dependencies
npm ci --only=production

# Build TypeScript
npx tsc

# Start with PM2
pm2 start dist/index.js --name a3a-bot
pm2 save
pm2 startup
```

#### Option B: Docker Deployment

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER
```

### 2. SSH Key Setup

```bash
# Generate SSH key pair
ssh-keygen -t rsa -b 4096 -C "github-actions-a3a-bot"

# Copy public key to server
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# Copy private key for GitHub Secrets
cat ~/.ssh/id_rsa
```

### 3. GitHub Secrets Configuration

Go to your repository → Settings → Secrets and variables → Actions

#### Required Secrets:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `HOST` | Server IP address | `*************` |
| `USERNAME` | SSH username | `ubuntu` |
| `PRIVATE_KEY` | SSH private key | `-----BEGIN RSA PRIVATE KEY-----...` |
| `NODE_BOT_TOKEN` | Telegram bot token | `1234567890:ABC...` |

#### Optional Secrets:

| Secret Name | Description | Default |
|-------------|-------------|---------|
| `PORT` | SSH port | `22` |
| `PROJECT_PATH` | Project directory | `~/a3a-telegram-bot` |
| `PROXY_URL` | Proxy URL if needed | - |

### 4. Environment Setup

Create environment files on your server:

```bash
# Production environment
cat > .env.production << EOF
NODE_ENV=production
NODE_BOT_TOKEN=your_bot_token_here
# PROXY_URL=http://proxy:port (if needed)
EOF
```

## 🔄 Deployment Workflows

### Automatic Deployment

- **Push to `main`** → Triggers production deployment
- **Pull Request** → Triggers CI tests only

### Manual Deployment

1. Go to Actions tab in GitHub
2. Select "CD - Deploy to Server" workflow
3. Click "Run workflow"

## 🐳 Docker Deployment

### Local Testing

```bash
# Build image
docker build -t a3a-bot .

# Run container
docker run -d \
  --name a3a-bot \
  --restart unless-stopped \
  -e NODE_BOT_TOKEN="your_token" \
  a3a-bot
```

### Production with Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  a3a-bot:
    image: ghcr.io/your-username/a3a-telegram-bot:latest
    container_name: a3a-bot
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NODE_BOT_TOKEN=${NODE_BOT_TOKEN}
      - PROXY_URL=${PROXY_URL}
    volumes:
      - ./logs:/app/logs
```

## 🔍 Monitoring & Troubleshooting

### PM2 Commands

```bash
# Check status
pm2 status

# View logs
pm2 logs a3a-bot

# Restart
pm2 restart a3a-bot

# Stop
pm2 stop a3a-bot
```

### Docker Commands

```bash
# Check container status
docker ps

# View logs
docker logs a3a-bot

# Restart container
docker restart a3a-bot
```

## 🛡️ Security Best Practices

1. **Use environment-specific secrets**
2. **Enable GitHub environment protection rules**
3. **Regularly rotate SSH keys**
4. **Use non-root user for deployment**
5. **Enable firewall on server**
6. **Keep dependencies updated**

## 📊 Workflow Features

### CI Pipeline
- ✅ Multi-Node.js version testing (18.x, 20.x)
- ✅ TypeScript compilation check
- ✅ Security vulnerability scanning
- ✅ Linting (if configured)
- ✅ Testing (if configured)

### CD Pipeline
- ✅ Production environment protection
- ✅ Automatic dependency installation
- ✅ TypeScript compilation
- ✅ PM2 process management
- ✅ Health checks
- ✅ Deployment notifications

### Docker Pipeline
- ✅ Multi-architecture builds (amd64, arm64)
- ✅ GitHub Container Registry
- ✅ Build caching
- ✅ Security scanning
- ✅ Automated container deployment

## 🚨 Troubleshooting

### Common Issues

1. **Permission denied (publickey)**
   - Check SSH key configuration
   - Verify authorized_keys permissions

2. **PM2 command not found**
   - Install PM2 globally: `sudo npm install -g pm2`

3. **TypeScript compilation errors**
   - Check tsconfig.json configuration
   - Verify all dependencies are installed

4. **Bot token issues**
   - Verify token in GitHub Secrets
   - Check environment variable loading

## 📞 Support

For issues and questions:
- Check GitHub Actions logs
- Review server logs (`pm2 logs` or `docker logs`)
- Verify environment variables
- Check network connectivity
