# 🚀 简洁部署方案设置指南

## 📋 方案概述

这是一个简洁高效的部署方案：

- GitHub Actions 编译 TypeScript 并打包
- 通过 SSH 传输部署包到服务器
- 服务器自动安装依赖并用 PM2 重启应用
- 无需服务器 Git 权限，安全可靠

## 🔧 服务器准备

### 1. 安装 Node.js 22.x

```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version  # 应该显示 v22.x.x
npm --version
```

### 2. 安装 PM2

```bash
sudo npm install -g pm2

# 设置开机自启
pm2 startup
# 按提示执行显示的命令
```

### 3. 创建部署目录

```bash
mkdir -p ~/worker/a3a-telegram-bot
cd ~/worker/a3a-telegram-bot

# 创建日志目录
mkdir -p logs
```

## 🔑 GitHub Secrets 配置

在 GitHub 仓库的 **Settings** → **Secrets and variables** → **Actions** 中添加：

### 必需的 Secrets

| Secret 名称        | 说明               | 示例                                   |
| ------------------ | ------------------ | -------------------------------------- |
| `HOST`           | 服务器 IP 地址     | `*************`                      |
| `USERNAME`       | SSH 用户名         | `ubuntu`                             |
| `PRIVATE_KEY`    | SSH 私钥           | `-----BEGIN RSA PRIVATE KEY-----...` |

### 可选的 Secrets

| Secret 名称     | 说明               | 默认值                 |
| --------------- | ------------------ | ---------------------- |
| `PORT`        | SSH 端口           | `22`                 |
| `DEPLOY_PATH` | 部署目录           | `~/a3a-telegram-bot` |

### 环境变量配置

所有环境变量（包括 Bot Token、代理设置等）都通过仓库中的 `.env` 文件管理。
确保你的 `.env` 文件已经提交到仓库中，包含所有必要的配置：

```env
NODE_ENV=production
NODE_BOT_TOKEN=你的机器人token
NODE_OFFICIAL_SITE=你的官方网站
NODE_OFFICIAL_STATIC=你的静态资源地址
# PROXY_URL=http://proxy:port (如果需要代理)
```

## 🔐 SSH 密钥设置

### 1. 生成 SSH 密钥对

```bash
# 在本地生成密钥对
ssh-keygen -t rsa -b 4096 -C "github-actions-deploy"

# 会生成两个文件：
# ~/.ssh/id_rsa (私钥 - 用于 GitHub Secret)
# ~/.ssh/id_rsa.pub (公钥 - 添加到服务器)
```

### 2. 添加公钥到服务器

```bash
# 在服务器上执行
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 将公钥内容添加到 authorized_keys
echo "你的公钥内容" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

### 3. 测试 SSH 连接

```bash
# 在本地测试连接
ssh -i ~/.ssh/id_rsa username@your-server-ip
```

## 🚀 部署流程

### 自动部署

推送代码到 main 分支即可自动部署：

```bash
git add .
git commit -m "Update bot"
git push origin main
```

### 手动部署

在 GitHub Actions 页面点击 "Run workflow" 按钮

## 📊 监控和管理

### PM2 常用命令

```bash
# 查看状态
pm2 status

# 查看日志
pm2 logs a3a-bot

# 重启应用
pm2 restart a3a-bot

# 停止应用
pm2 stop a3a-bot

# 查看详细信息
pm2 describe a3a-bot

# 监控面板
pm2 monit
```

### 日志文件位置

```bash
# 应用日志
~/a3a-telegram-bot/current/logs/

# PM2 日志
~/.pm2/logs/
```

## 🔍 故障排除

### 1. 部署失败

```bash
# 检查 GitHub Actions 日志
# 验证 SSH 连接
ssh username@your-server-ip

# 检查服务器磁盘空间
df -h

# 检查 Node.js 和 PM2
node --version
pm2 --version
```

### 2. 应用无法启动

```bash
# 查看 PM2 状态
pm2 status

# 查看错误日志
pm2 logs a3a-bot --err

# 手动启动测试
cd ~/a3a-telegram-bot/current
node dist/index.js
```

### 3. Bot 无响应

```bash
# 检查 Bot Token
curl -s "https://api.telegram.org/bot<YOUR_TOKEN>/getMe"

# 检查环境变量
cat ~/a3a-telegram-bot/current/.env

# 检查网络连接
ping api.telegram.org
```

## 📁 部署包结构

部署包包含以下文件：

```
deploy-package/
├── dist/           # 编译后的 JavaScript 文件
├── package.json    # 项目依赖信息
├── package-lock.json
├── ecosystem.config.js  # PM2 配置
└── .env           # 环境变量（从仓库复制）
```

## 🔄 回滚操作

如果需要回滚到上一个版本：

```bash
cd ~/a3a-telegram-bot

# 停止当前应用
pm2 stop a3a-bot

# 恢复备份版本
rm -rf current
mv backup current

# 重启应用
cd current
pm2 start ecosystem.config.js --env production
```

## ✅ 验证部署

1. **检查 PM2 状态**：`pm2 status` 应该显示 `a3a-bot` 为 `online`
2. **测试 Bot**：在 Telegram 中发送 `/start` 给你的 Bot
3. **查看日志**：`pm2 logs a3a-bot` 确认无错误

## 📞 获取帮助

- **GitHub Actions 失败**：查看 Actions 页面的详细日志
- **SSH 连接问题**：检查密钥配置和网络连接
- **应用启动失败**：查看 PM2 日志和应用日志
- **Bot 无响应**：验证 Token 和网络连接

---

**这个方案简洁、安全、易于维护。一旦设置完成，只需推送代码即可自动部署！** 🎉
