{"name": "a3a-ta-bot", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "NODE_ENV=development ts-node-dev --respawn src/index.ts", "dev:prod": "NODE_ENV=production ts-node-dev --respawn src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:prod": "NODE_ENV=production node dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "type-check": "tsc --noEmit", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'"}, "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^16.5.0", "telegraf": "^4.16.3"}, "devDependencies": {"https-proxy-agent": "^7.0.6", "@types/node": "^24.0.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}