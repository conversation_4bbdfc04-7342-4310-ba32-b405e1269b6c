name: Simple Deploy

on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  deploy:
    name: Simple Build and Deploy
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build TypeScript
      run: npm run build

    - name: Create deployment package
      run: |
        # Create deployment directory
        mkdir -p deploy-package

        # Copy built files
        cp -r dist deploy-package/

        # Copy necessary files
        cp package.json deploy-package/
        cp package-lock.json deploy-package/

        # Copy PM2 config if exists
        if [ -f "ecosystem.config.js" ]; then
          cp ecosystem.config.js deploy-package/
        fi

        # Copy .env file if exists
        if [ -f ".env" ]; then
          cp .env deploy-package/
        else
          echo "Warning: .env file not found in repository"
        fi

        # Create deployment archive
        tar -czf deploy-package.tar.gz -C deploy-package .

        # Show what we created
        echo "Created deployment package:"
        ls -la deploy-package.tar.gz
        echo "Package contents:"
        tar -tzf deploy-package.tar.gz

    - name: Debug server environment
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          echo "=== Server Environment Debug ==="
          echo "Current user: $(whoami)"
          echo "Home directory: $HOME"
          echo "Current directory: $(pwd)"
          echo "Default PATH: $PATH"

          # Try to find Node.js in common locations
          echo "Looking for Node.js..."
          which node || echo "node not in PATH"
          ls -la /usr/bin/node* 2>/dev/null || echo "No node in /usr/bin"
          ls -la /usr/local/bin/node* 2>/dev/null || echo "No node in /usr/local/bin"

          # Check if nvm is installed
          if [ -s "$HOME/.nvm/nvm.sh" ]; then
            echo "NVM found, loading..."
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
          fi

          # Check if n is installed
          if [ -d "/usr/local/n" ]; then
            echo "n (Node version manager) found"
            export N_PREFIX="/usr/local/n"
            export PATH="/usr/local/n/bin:$PATH"
          fi

          # Update PATH to include common Node.js locations
          export PATH="/usr/local/bin:/usr/bin:$PATH"

          echo "Updated PATH: $PATH"
          echo "Node.js version: $(node --version 2>/dev/null || echo 'Node.js not found')"
          echo "npm version: $(npm --version 2>/dev/null || echo 'npm not found')"
          echo "PM2 version: $(pm2 --version 2>/dev/null || echo 'PM2 not found')"
          echo "=== End Debug ==="

    - name: Transfer deployment package
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        source: "deploy-package.tar.gz"
        target: "~/"

    - name: Deploy application
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          echo "=== Starting Deployment ==="

          # Set deployment directory
          DEPLOY_DIR="/home/<USER>/worker/a3a-telegram-bot"

          # Create deployment directory if not exists
          mkdir -p $DEPLOY_DIR
          cd $DEPLOY_DIR

          echo "Working in directory: $(pwd)"

          # Check if deployment package exists
          if [ ! -f "~/deploy-package.tar.gz" ]; then
            echo "Error: deploy-package.tar.gz not found in home directory"
            ls -la ~/
            exit 1
          fi

          # Move deployment package to working directory
          mv ~/deploy-package.tar.gz ./

          # Backup current version
          if [ -d "current" ]; then
            echo "Backing up current version..."
            rm -rf backup 2>/dev/null || true
            mv current backup 2>/dev/null || true
          fi

          # Create new deployment directory
          mkdir -p current

          # Extract deployment package
          echo "Extracting deployment package..."
          tar -xzf deploy-package.tar.gz -C current/
          rm deploy-package.tar.gz

          # Enter deployment directory
          cd current
          echo "Deployment directory contents:"
          ls -la

          # Set up Node.js environment
          echo "Setting up Node.js environment..."

          # Check if nvm is installed
          if [ -s "$HOME/.nvm/nvm.sh" ]; then
            echo "Loading NVM..."
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
          fi

          # Check if n is installed
          if [ -d "/usr/local/n" ]; then
            echo "Loading n (Node version manager)..."
            export N_PREFIX="/usr/local/n"
            export PATH="/usr/local/n/bin:$PATH"
          fi

          # Update PATH to include common Node.js locations
          export PATH="/usr/local/bin:/usr/bin:$PATH"

          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"

          # Install production dependencies
          echo "Installing dependencies..."
          npm ci --only=production --no-audit --no-fund

          # Restart application with PM2
          echo "Restarting application..."
          if command -v pm2 >/dev/null 2>&1; then
            # Stop existing process
            pm2 delete a3a-bot 2>/dev/null || true

            # Start new process
            if [ -f "ecosystem.config.js" ]; then
              pm2 start ecosystem.config.js --env production
            else
              pm2 start dist/index.js --name a3a-bot
            fi

            pm2 save

            # Show status
            pm2 status

            echo "✅ Deployment completed successfully!"
          else
            echo "❌ PM2 not found"
            exit 1
          fi
