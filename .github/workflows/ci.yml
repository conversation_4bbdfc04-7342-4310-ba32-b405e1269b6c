name: CI - Build and Test

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]

jobs:
  test:
    name: Test and Build
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript compilation
      run: npx tsc --noEmit
      
    - name: Run linting (if configured)
      run: |
        if [ -f "package.json" ] && npm run lint --if-present; then
          echo "Lin<PERSON> completed"
        else
          echo "No linting configured, skipping"
        fi
      continue-on-error: true
      
    - name: Run tests (if configured)
      run: |
        if [ -f "package.json" ] && npm test --if-present; then
          echo "Tests completed"
        else
          echo "No tests configured, skipping"
        fi
      continue-on-error: true

  security-scan:
    name: <PERSON> Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run npm audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
      
    - name: Check for vulnerabilities
      run: |
        if npm audit --audit-level=high; then
          echo "No high-severity vulnerabilities found"
        else
          echo "High-severity vulnerabilities detected"
          exit 1
        fi
