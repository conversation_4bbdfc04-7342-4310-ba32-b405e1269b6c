name: CI - Build and Test

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]

# Security: Restrict GITHUB_TOKEN permissions
permissions:
  contents: read
  security-events: write
  actions: read

jobs:
  test:
    name: Test and Build
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x, 22.x]  # Updated to latest LTS versions

    steps:
    - name: Checkout code
      uses: actions/checkout@v4.2.2  # Latest version

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4.4.0  # Latest version
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: npm ci

    - name: Run TypeScript compilation
      run: npx tsc --noEmit

    - name: Run linting (if configured)
      run: |
        if [ -f "package.json" ] && npm run lint --if-present; then
          echo "Linting completed"
        else
          echo "No linting configured, skipping"
        fi
      continue-on-error: true

    - name: Run tests (if configured)
      run: |
        if [ -f "package.json" ] && npm test --if-present; then
          echo "Tests completed"
        else
          echo "No tests configured, skipping"
        fi
      continue-on-error: true

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4.2.2

    - name: Setup Node.js
      uses: actions/setup-node@v4.4.0
      with:
        node-version: '22.x'
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: npm ci

    - name: Run npm audit
      run: npm audit --audit-level=moderate
      continue-on-error: true

    - name: Check for vulnerabilities
      run: |
        if npm audit --audit-level=high; then
          echo "No high-severity vulnerabilities found"
        else
          echo "High-severity vulnerabilities detected"
          exit 1
        fi

    - name: Run CodeQL Analysis (if applicable)
      uses: github/codeql-action/init@v3
      with:
        languages: javascript
      continue-on-error: true

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      continue-on-error: true
