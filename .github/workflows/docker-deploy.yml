name: <PERSON><PERSON> Deploy (Alternative)

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

# Security: Minimal permissions
permissions:
  contents: read

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write  # For OIDC

    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4.2.2

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3.7.1  # Latest version

    - name: Login to Container Registry
      uses: docker/login-action@v3.3.0  # Latest version
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5.5.1  # Latest version
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v6.9.0  # Latest version
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        # Security: Build attestations
        provenance: true
        sbom: true

    - name: Output image
      id: image
      run: echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}" >> $GITHUB_OUTPUT

  deploy-docker:
    name: Deploy Docker Container
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: production

    steps:
    - name: Deploy to server via SSH
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # Login to GitHub Container Registry
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin

          # Pull the latest image
          docker pull ${{ needs.build-and-push.outputs.image }}

          # Stop and remove existing container
          docker stop a3a-bot || true
          docker rm a3a-bot || true

          # Run new container
          docker run -d \
            --name a3a-bot \
            --restart unless-stopped \
            -e NODE_ENV=production \
            -e NODE_BOT_TOKEN="${{ secrets.NODE_BOT_TOKEN }}" \
            -e PROXY_URL="${{ secrets.PROXY_URL }}" \
            ${{ needs.build-and-push.outputs.image }}

          # Health check
          sleep 10
          docker ps | grep a3a-bot
