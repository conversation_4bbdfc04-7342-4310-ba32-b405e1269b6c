name: <PERSON><PERSON> Deploy (Alternative)

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      
    outputs:
      image: ${{ steps.image.outputs.image }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Output image
      id: image
      run: echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}" >> $GITHUB_OUTPUT

  deploy-docker:
    name: Deploy Docker Container
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: production
    
    steps:
    - name: Deploy to server via SSH
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # Login to GitHub Container Registry
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
          
          # Pull the latest image
          docker pull ${{ needs.build-and-push.outputs.image }}
          
          # Stop and remove existing container
          docker stop a3a-bot || true
          docker rm a3a-bot || true
          
          # Run new container
          docker run -d \
            --name a3a-bot \
            --restart unless-stopped \
            -e NODE_ENV=production \
            -e NODE_BOT_TOKEN="${{ secrets.NODE_BOT_TOKEN }}" \
            -e PROXY_URL="${{ secrets.PROXY_URL }}" \
            ${{ needs.build-and-push.outputs.image }}
          
          # Health check
          sleep 10
          docker ps | grep a3a-bot
