name: CD - Deploy to Server

on:
  push:
    branches: [ main ]
  workflow_dispatch: # Allow manual deployment

# Security: Minimal permissions
permissions:
  contents: read
  deployments: write

jobs:
  deploy:
    name: Deploy to Production Server
    runs-on: ubuntu-latest
    environment: production # GitHub environment for additional security

    # Security: Concurrency control
    concurrency:
      group: production-deployment
      cancel-in-progress: false

    steps:
    - name: Checkout code
      uses: actions/checkout@v4.2.2

    - name: Setup Node.js
      uses: actions/setup-node@v4.4.0
      with:
        node-version: '22.x'  # Latest LTS
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: npm ci --only=production

    - name: Build TypeScript
      run: npx tsc

    - name: Deploy to server via SSH
      uses: appleboy/ssh-action@v1.1.0  # Updated to latest version
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        timeout: 300s  # 5 minute timeout
        command_timeout: 600s  # 10 minute command timeout
        script: |
          # Set strict error handling
          set -euo pipefail

          # Navigate to project directory
          cd ${{ secrets.PROJECT_PATH || '~/a3a-telegram-bot' }}

          # Backup current version (optional)
          if [ -d "dist" ]; then
            cp -r dist dist.backup.$(date +%Y%m%d_%H%M%S) || true
          fi

          # Pull latest changes
          git pull origin main

          # Install/update dependencies with npm ci for reproducible builds
          npm ci --only=production --no-audit --no-fund

          # Build the project
          npm run build --if-present || npx tsc

          # Restart the application with PM2
          if command -v pm2 &> /dev/null; then
            # Use ecosystem file if available
            if [ -f "ecosystem.config.js" ]; then
              pm2 reload ecosystem.config.js --env production
            else
              pm2 restart a3a-bot || pm2 start dist/index.js --name a3a-bot
            fi
          else
            echo "PM2 not found, please install PM2 or use alternative process manager"
            # Alternative: restart with systemd
            # sudo systemctl restart a3a-bot
          fi

          # Health check with retry
          echo "Performing health check..."
          for i in {1..5}; do
            sleep 2
            if command -v pm2 &> /dev/null; then
              if pm2 status a3a-bot | grep -q "online"; then
                echo "✅ Deployment successful - Bot is online"
                exit 0
              fi
            fi
            echo "Attempt $i/5: Bot not ready yet..."
          done

          echo "⚠️ Health check completed, please verify manually"
          pm2 status a3a-bot || true

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: deploy
    if: always()

    steps:
    - name: Notify success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Deployment successful!"
        # Add notification logic here (Slack, Discord, etc.)

    - name: Notify failure
      if: needs.deploy.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        # Add notification logic here (Slack, Discord, etc.)
        exit 1
