name: CD - Deploy to Server

on:
  push:
    branches: [ main ]
  workflow_dispatch: # Allow manual deployment

jobs:
  deploy:
    name: Deploy to Production Server
    runs-on: ubuntu-latest
    environment: production # GitHub environment for additional security
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --only=production
      
    - name: Build TypeScript
      run: npx tsc
      
    - name: Deploy to server via SSH
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # Navigate to project directory
          cd ${{ secrets.PROJECT_PATH || '~/a3a-telegram-bot' }}
          
          # Pull latest changes
          git pull origin main
          
          # Install/update dependencies
          npm ci --only=production
          
          # Build the project
          npm run build --if-present || npx tsc
          
          # Restart the application with PM2
          if command -v pm2 &> /dev/null; then
            pm2 restart a3a-bot || pm2 start dist/index.js --name a3a-bot
          else
            echo "PM2 not found, please install PM2 or use alternative process manager"
            # Alternative: restart with systemd
            # sudo systemctl restart a3a-bot
          fi
          
          # Health check
          sleep 5
          if command -v pm2 &> /dev/null; then
            pm2 status a3a-bot
          fi

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: deploy
    if: always()
    
    steps:
    - name: Notify success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Deployment successful!"
        # Add notification logic here (Slack, Discord, etc.)
        
    - name: Notify failure
      if: needs.deploy.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        # Add notification logic here (Slack, Discord, etc.)
        exit 1
