name: Deploy to Server

on:
  push:
    branches: [ main ]
  workflow_dispatch:

permissions:
  contents: read

jobs:
  deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build TypeScript
      run: npm run build

    - name: Create deployment package
      run: |
        # Create deployment directory
        mkdir -p deploy-package

        # Copy built files
        cp -r dist deploy-package/

        # Copy necessary files
        cp package.json deploy-package/
        cp package-lock.json deploy-package/

        # Copy PM2 config if exists
        if [ -f "ecosystem.config.js" ]; then
          cp ecosystem.config.js deploy-package/
        fi

        # Copy .env file directly from repository
        if [ -f ".env" ]; then
          cp .env deploy-package/
        else
          echo "Warning: .env file not found in repository"
        fi

        # Create deployment archive
        tar -czf deploy-package.tar.gz -C deploy-package .

    - name: Deploy to server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # Set deployment directory
          DEPLOY_DIR="${{ secrets.DEPLOY_PATH || '~/worker/a3a-telegram-bot' }}"

          # Create deployment directory if not exists
          mkdir -p $DEPLOY_DIR
          cd $DEPLOY_DIR

          # Backup current version (optional)
          if [ -d "current" ]; then
            rm -rf backup 2>/dev/null || true
            mv current backup 2>/dev/null || true
          fi

          # Create new deployment directory
          mkdir -p current

    - name: Transfer deployment package
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        source: "deploy-package.tar.gz"
        target: "${{ secrets.DEPLOY_PATH || '~/a3a-telegram-bot' }}"

    - name: Extract and restart application
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # Set deployment directory
          DEPLOY_DIR="${{ secrets.DEPLOY_PATH || '~/worker/a3a-telegram-bot' }}"
          cd $DEPLOY_DIR

          # Extract deployment package
          tar -xzf deploy-package.tar.gz -C current/
          rm deploy-package.tar.gz

          # Enter deployment directory
          cd current

          # Install production dependencies
          npm ci --only=production --no-audit --no-fund

          # Restart application with PM2
          if command -v pm2 >/dev/null 2>&1; then
            # Use ecosystem config if available
            if [ -f "ecosystem.config.js" ]; then
              pm2 reload ecosystem.config.js --env production
            else
              # Simple PM2 restart
              pm2 delete a3a-bot 2>/dev/null || true
              pm2 start dist/index.js --name a3a-bot --env production
            fi
            pm2 save
          else
            echo "PM2 not found. Please install PM2 or use alternative process manager."
            exit 1
          fi

          # Health check
          sleep 3
          if pm2 list | grep -q "a3a-bot.*online"; then
            echo "✅ Deployment successful - Bot is running"
          else
            echo "❌ Deployment may have issues - Check PM2 status"
            pm2 status
            exit 1
          fi
