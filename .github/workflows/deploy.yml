name: Deploy (Fixed)

on:
  push:
    branches: [ main ]
  workflow_dispatch:

permissions:
  contents: read

jobs:
  deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build TypeScript
      run: npm run build

    - name: Create deployment package
      run: |
        # Create deployment directory
        mkdir -p deploy-package

        # Copy built files
        cp -r dist deploy-package/

        # Copy necessary files
        cp package.json deploy-package/
        cp package-lock.json deploy-package/

        # Copy PM2 config if exists
        if [ -f "ecosystem.config.js" ]; then
          cp ecosystem.config.js deploy-package/
        fi

        # Copy .env file if exists
        if [ -f ".env" ]; then
          cp .env deploy-package/
        else
          echo "Warning: .env file not found in repository"
        fi

        # Create deployment archive
        tar -czf deploy-package.tar.gz -C deploy-package .

    - name: Find Node.js path on server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          echo "Finding Node.js and PM2 paths..."
          echo "which node: $(which node 2>/dev/null || echo 'not found')"
          echo "which npm: $(which npm 2>/dev/null || echo 'not found')"
          echo "which pm2: $(which pm2 2>/dev/null || echo 'not found')"

          # Try common locations
          for path in /usr/local/bin/node /usr/bin/node ~/.nvm/versions/node/*/bin/node; do
            if [ -x "$path" ]; then
              echo "Found node at: $path"
              echo "Version: $($path --version)"
              break
            fi
          done

          for path in /usr/local/bin/npm /usr/bin/npm ~/.nvm/versions/node/*/bin/npm; do
            if [ -x "$path" ]; then
              echo "Found npm at: $path"
              echo "Version: $($path --version)"
              break
            fi
          done

          for path in /usr/local/bin/pm2 /usr/bin/pm2 ~/.nvm/versions/node/*/bin/pm2; do
            if [ -x "$path" ]; then
              echo "Found pm2 at: $path"
              echo "Version: $($path --version)"
              break
            fi
          done

    - name: Transfer and deploy
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        source: "deploy-package.tar.gz"
        target: "~/"

    - name: Deploy application
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          echo "=== Starting Deployment ==="

          # Find Node.js and npm paths
          NODE_PATH=""
          NPM_PATH=""
          PM2_PATH=""

          # Try to find node
          for path in /usr/local/bin/node /usr/bin/node ~/.nvm/versions/node/*/bin/node; do
            if [ -x "$path" ]; then
              NODE_PATH="$path"
              break
            fi
          done

          # Try to find npm
          for path in /usr/local/bin/npm /usr/bin/npm ~/.nvm/versions/node/*/bin/npm; do
            if [ -x "$path" ]; then
              NPM_PATH="$path"
              break
            fi
          done

          # Try to find pm2
          for path in /usr/local/bin/pm2 /usr/bin/pm2 ~/.nvm/versions/node/*/bin/pm2; do
            if [ -x "$path" ]; then
              PM2_PATH="$path"
              break
            fi
          done

          echo "Using Node.js: $NODE_PATH"
          echo "Using npm: $NPM_PATH"
          echo "Using PM2: $PM2_PATH"

          if [ -z "$NODE_PATH" ] || [ -z "$NPM_PATH" ] || [ -z "$PM2_PATH" ]; then
            echo "Error: Could not find Node.js, npm, or PM2"
            exit 1
          fi

          # Set deployment directory
          DEPLOY_DIR="/home/<USER>/worker/a3a-telegram-bot"

          # Create deployment directory if not exists
          mkdir -p $DEPLOY_DIR
          cd $DEPLOY_DIR

          # Backup current version
          if [ -d "current" ]; then
            echo "Backing up current version..."
            rm -rf backup 2>/dev/null || true
            mv current backup 2>/dev/null || true
          fi

          # Create new deployment directory
          mkdir -p current

          # Extract deployment package
          echo "Extracting deployment package..."
          tar -xzf ~/deploy-package.tar.gz -C current/
          rm ~/deploy-package.tar.gz

          # Enter deployment directory
          cd current

          # Install production dependencies using absolute path
          echo "Installing dependencies..."
          $NPM_PATH ci --only=production --no-audit --no-fund

          # Restart application with PM2
          echo "Restarting application..."

          # Stop existing process
          $PM2_PATH delete a3a-bot 2>/dev/null || true

          # Start new process
          if [ -f "ecosystem.config.js" ]; then
            $PM2_PATH start ecosystem.config.js --env production
          else
            $PM2_PATH start dist/index.js --name a3a-bot
          fi

          $PM2_PATH save

          # Show status
          $PM2_PATH status

          echo "✅ Deployment completed successfully!"
