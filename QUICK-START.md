# 🚀 快速开始指南

## 📋 简化的部署方案

这个方案非常简单：
1. GitHub Actions 编译代码并打包
2. 直接复制仓库中的 .env 文件
3. 通过 SSH 传输到服务器并自动部署

## 🔧 准备工作

### 1. 服务器准备

```bash
# 安装 Node.js 22.x
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
sudo npm install -g pm2
pm2 startup

# 创建部署目录
mkdir -p ~/a3a-telegram-bot/logs
```

### 2. SSH 密钥设置

```bash
# 生成密钥对
ssh-keygen -t rsa -b 4096 -C "github-actions"

# 将公钥添加到服务器
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# 私钥内容用于 GitHub Secret
cat ~/.ssh/id_rsa
```

### 3. GitHub Secrets 配置

在仓库 **Settings** → **Secrets and variables** → **Actions** 中添加：

| Secret 名称 | 说明 | 示例 |
|-------------|------|------|
| `HOST` | 服务器 IP | `*************` |
| `USERNAME` | SSH 用户名 | `ubuntu` |
| `PRIVATE_KEY` | SSH 私钥 | `-----BEGIN RSA PRIVATE KEY-----...` |

### 4. 环境变量文件

确保你的 `.env` 文件已经提交到仓库，包含：

```env
NODE_ENV=production
NODE_BOT_TOKEN=你的机器人token
NODE_OFFICIAL_SITE=你的官方网站
NODE_OFFICIAL_STATIC=你的静态资源地址
# PROXY_URL=http://proxy:port (如果需要)
```

## 🚀 部署

推送代码到 main 分支即可自动部署：

```bash
git add .
git commit -m "Deploy bot"
git push origin main
```

## 📊 监控

```bash
# 查看状态
pm2 status

# 查看日志
pm2 logs a3a-bot

# 重启
pm2 restart a3a-bot
```

## 🔍 故障排除

### 部署失败
1. 检查 GitHub Actions 日志
2. 验证 SSH 连接：`ssh username@server-ip`
3. 检查服务器空间：`df -h`

### Bot 无响应
1. 检查进程：`pm2 status`
2. 查看日志：`pm2 logs a3a-bot`
3. 验证 Token：`curl -s "https://api.telegram.org/bot<TOKEN>/getMe"`

---

**就这么简单！推送代码即可自动部署。** 🎉
