# 🚀 A3A Telegram Bot - 快速开始指南

## 📋 部署前准备

### 1. 获取 Telegram Bot Token
1. 在 Telegram 中找到 [@BotFather](https://t.me/botfather)
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称和用户名
4. 保存获得的 Bot Token

### 2. 准备服务器
- Ubuntu 18.04+ 或 CentOS 7+
- 至少 1GB RAM
- SSH 访问权限
- 域名或 IP 地址

## 🛠️ 方式一：自动化部署（推荐）

### 步骤 1: 服务器初始化

在服务器上运行：
```bash
# 克隆项目
git clone https://github.com/your-username/a3a-telegram-bot.git
cd a3a-telegram-bot

# 运行自动化设置脚本
chmod +x scripts/setup-server.sh
./scripts/setup-server.sh
```

### 步骤 2: 配置 GitHub Secrets

在 GitHub 仓库中设置以下 Secrets：

| Secret 名称 | 说明 | 示例 |
|-------------|------|------|
| `HOST` | 服务器 IP 地址 | `*************` |
| `USERNAME` | SSH 用户名 | `ubuntu` |
| `PRIVATE_KEY` | SSH 私钥 | `-----BEGIN RSA PRIVATE KEY-----...` |
| `NODE_BOT_TOKEN` | Telegram Bot Token | `1234567890:ABC...` |

### 步骤 3: 部署

推送代码到 main 分支即可自动部署：
```bash
git push origin main
```

## 🐳 方式二：Docker 部署

### 快速启动
```bash
# 构建镜像
docker build -t a3a-bot .

# 运行容器
docker run -d \
  --name a3a-bot \
  --restart unless-stopped \
  -e NODE_BOT_TOKEN="你的机器人token" \
  a3a-bot
```

### 使用 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  a3a-bot:
    build: .
    container_name: a3a-bot
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NODE_BOT_TOKEN=你的机器人token
    volumes:
      - ./logs:/app/logs
```

运行：
```bash
docker-compose up -d
```

## 🔧 方式三：手动部署

### 1. 安装依赖
```bash
# 安装 Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
sudo npm install -g pm2
```

### 2. 部署应用
```bash
# 克隆项目
git clone https://github.com/your-username/a3a-telegram-bot.git
cd a3a-telegram-bot

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.example .env.production
nano .env.production  # 编辑添加你的 bot token

# 构建项目
npm run build

# 启动服务
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 📊 监控和管理

### PM2 常用命令
```bash
pm2 status          # 查看状态
pm2 logs a3a-bot    # 查看日志
pm2 restart a3a-bot # 重启服务
pm2 stop a3a-bot    # 停止服务
```

### Docker 常用命令
```bash
docker ps                    # 查看容器状态
docker logs a3a-bot         # 查看日志
docker restart a3a-bot      # 重启容器
docker stop a3a-bot         # 停止容器
```

## 🔍 故障排除

### 常见问题

1. **机器人无响应**
   ```bash
   # 检查进程状态
   pm2 status
   
   # 查看日志
   pm2 logs a3a-bot
   
   # 检查 token 是否正确
   curl -s "https://api.telegram.org/bot你的token/getMe"
   ```

2. **部署失败**
   ```bash
   # 检查 GitHub Actions 日志
   # 验证 GitHub Secrets 配置
   # 确认服务器 SSH 连接正常
   ssh -i ~/.ssh/your_key user@your_server
   ```

3. **TypeScript 编译错误**
   ```bash
   # 检查类型错误
   npm run type-check
   
   # 清理重新构建
   npm run clean
   npm run build
   ```

## 🚀 快速更新

使用快速部署脚本：
```bash
# 在服务器上运行
./scripts/deploy.sh
```

## 📞 获取帮助

1. 查看完整文档：`README.md`
2. 查看部署指南：`deployment-guide.md`
3. 检查 GitHub Actions 日志
4. 查看服务器日志：`pm2 logs a3a-bot`

## ✅ 部署成功验证

1. 在 Telegram 中找到你的机器人
2. 发送 `/start` 命令
3. 机器人应该回复欢迎消息和按钮
4. 检查服务器状态：`pm2 status`

恭喜！你的 A3A Telegram Bot 已经成功部署！🎉
