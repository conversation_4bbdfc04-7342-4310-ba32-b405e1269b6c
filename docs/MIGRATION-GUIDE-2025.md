# 🔄 迁移指南：升级到 2025 年最佳实践

## 📋 概述

本指南将帮助你从 2024 年的配置升级到 2025 年的最新最佳实践。

## 🚀 主要变化

### 1. Node.js 版本升级

#### 从 Node.js 18.x/20.x 升级到 22.x

**服务器端升级**:
```bash
# 备份当前环境
pm2 save
pm2 dump

# 升级 Node.js
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证版本
node --version  # 应该显示 v22.x.x
npm --version

# 重新安装依赖
cd /path/to/your/project
rm -rf node_modules package-lock.json
npm install
npm run build

# 重启应用
pm2 restart all
```

### 2. GitHub Actions 更新

#### 更新 workflow 文件

**CI 工作流 (.github/workflows/ci.yml)**:
```yaml
# 旧版本
- uses: actions/checkout@v4
- uses: actions/setup-node@v4
  with:
    node-version: [18.x, 20.x]

# 新版本
- uses: actions/checkout@v4.2.2
- uses: actions/setup-node@v4.4.0
  with:
    node-version: [20.x, 22.x]
    cache-dependency-path: 'package-lock.json'
```

**添加安全权限**:
```yaml
# 在每个 workflow 文件顶部添加
permissions:
  contents: read
  security-events: write
  actions: read
```

### 3. Docker 配置更新

#### Dockerfile 升级

**更新基础镜像**:
```dockerfile
# 旧版本
FROM node:20-alpine

# 新版本
FROM node:22-alpine

# 添加安全更新
RUN apk upgrade --no-cache
```

**增强安全配置**:
```dockerfile
# 添加环境变量优化
ENV NODE_OPTIONS="--max-old-space-size=512"

# 改进健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD node -e "console.log('Health check passed at', new Date().toISOString())" || exit 1
```

## 📝 逐步迁移清单

### 步骤 1: 准备工作

- [ ] 备份当前配置
- [ ] 创建新的分支进行测试
- [ ] 确保有回滚计划

### 步骤 2: 更新配置文件

- [ ] 更新 `.github/workflows/ci.yml`
- [ ] 更新 `.github/workflows/deploy.yml`
- [ ] 更新 `.github/workflows/docker-deploy.yml`
- [ ] 更新 `Dockerfile`
- [ ] 更新 `scripts/setup-server.sh`

### 步骤 3: 测试环境验证

- [ ] 在测试环境部署新配置
- [ ] 运行所有测试
- [ ] 验证安全扫描通过
- [ ] 检查性能指标

### 步骤 4: 生产环境部署

- [ ] 更新服务器 Node.js 版本
- [ ] 部署新的 workflow
- [ ] 监控部署过程
- [ ] 验证应用正常运行

## 🔧 配置对比

### GitHub Actions 版本对比

| 组件 | 2024 版本 | 2025 版本 | 变化说明 |
|------|-----------|-----------|----------|
| checkout | v4 | v4.2.2 | 最新稳定版本 |
| setup-node | v4 | v4.4.0 | 改进的缓存支持 |
| docker/build-push-action | v5 | v6.9.0 | 安全增强 |
| Node.js 版本 | 18.x, 20.x | 20.x, 22.x | LTS 版本更新 |

### 安全功能对比

| 功能 | 2024 | 2025 | 改进 |
|------|------|------|------|
| 权限控制 | 基础 | 最小化权限 | 更严格的安全控制 |
| 漏洞扫描 | npm audit | npm audit + CodeQL | 更全面的扫描 |
| Docker 安全 | 基础 | SBOM + 证明 | 供应链安全 |
| 并发控制 | 无 | 有 | 防止并发部署 |

## ⚠️ 注意事项

### 兼容性检查

1. **Node.js 22 兼容性**
   - 检查所有依赖是否支持 Node.js 22
   - 测试应用在新版本下的行为
   - 验证性能表现

2. **GitHub Actions 限制**
   - 检查组织的 Actions 策略
   - 验证新权限设置
   - 确认环境保护规则

3. **Docker 镜像**
   - 验证新镜像的兼容性
   - 检查镜像大小变化
   - 测试容器启动时间

### 常见问题

#### Q: 升级后构建失败怎么办？

A: 检查以下几点：
1. Node.js 版本兼容性
2. 依赖版本冲突
3. TypeScript 配置
4. 环境变量设置

#### Q: 部署权限错误？

A: 确保：
1. GitHub Secrets 正确配置
2. 环境保护规则已更新
3. 权限设置正确

#### Q: Docker 构建失败？

A: 检查：
1. 基础镜像可用性
2. 依赖安装过程
3. 多阶段构建配置

## 🔄 回滚计划

如果升级过程中遇到问题，可以按以下步骤回滚：

### 快速回滚

1. **恢复 workflow 文件**
   ```bash
   git checkout HEAD~1 -- .github/workflows/
   git commit -m "Rollback workflows to previous version"
   git push
   ```

2. **恢复服务器 Node.js 版本**
   ```bash
   # 如果有备份
   pm2 restore /path/to/backup

   # 或者重新安装旧版本
   curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. **恢复 Docker 镜像**
   ```bash
   # 使用之前的镜像标签
   docker pull your-registry/your-image:previous-tag
   docker stop your-container
   docker run -d --name your-container your-registry/your-image:previous-tag
   ```

## 📊 验证清单

### 功能验证

- [ ] 机器人正常响应命令
- [ ] 所有功能正常工作
- [ ] 性能指标正常
- [ ] 错误率在可接受范围

### 安全验证

- [ ] 安全扫描通过
- [ ] 权限设置正确
- [ ] 漏洞检查通过
- [ ] 访问控制有效

### 运维验证

- [ ] 监控正常工作
- [ ] 日志收集正常
- [ ] 告警配置有效
- [ ] 备份策略更新

## 📞 获取帮助

如果在迁移过程中遇到问题：

1. **查看文档**
   - `BEST-PRACTICES-2025.md`
   - `README.md`
   - `deployment-guide.md`

2. **检查日志**
   - GitHub Actions 日志
   - 服务器应用日志
   - Docker 容器日志

3. **社区支持**
   - GitHub Issues
   - 相关技术社区
   - 官方文档

---

**迁移完成后，请更新团队文档并培训相关人员使用新的最佳实践。**
